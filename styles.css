body {
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    background-color: #faf8ef;
}

.container {
    text-align: center;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

}

h1 {
    font-size: 3rem;
    margin: 0;
    color: #776e65;
    
}

.score-container {
    display: flex;
    gap: 20px;
}

.score, .best-score {
    background-color: #bbada0;
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-weight: bold;
}

.game-container {
    position: relative;
    width: 500px;
    height: 500px;
    background-color: #bbada0;
    border-radius: 10px;
    padding: 15px;
    box-sizing: border-box;
}

.grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px;
    height: 100%;
}

.grid-cell {
    background-color: rgba(238, 228, 218, 0.35);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    font-weight: bold;
    color: #776e65;
    transition: all 0.2s ease-in-out;
}

.grid-cell:not(:empty) {
    color: white;
}

.grid-cell.tile-2 { background-color: #eee4da; }
.grid-cell.tile-4 { background-color: #ede0c8; }
.grid-cell.tile-8 { background-color: #f2b179; color: white; }
.grid-cell.tile-16 { background-color: #f59563; color: white; }
.grid-cell.tile-32 { background-color: #f67c5f; color: white; }
.grid-cell.tile-64 { background-color: #f65e3b; color: white; }
.grid-cell.tile-128 { background-color: #edcf72; color: white; font-size: 1.8rem; }
.grid-cell.tile-256 { background-color: #edcc61; color: white; font-size: 1.8rem; }
.grid-cell.tile-512 { background-color: #edc850; color: white; font-size: 1.6rem; }
.grid-cell.tile-1024 { background-color: #edc53f; color: white; font-size: 1.4rem; }
.grid-cell.tile-2048 { background-color: #edc22e; color: white; font-size: 1.2rem; }

.game-over {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(238, 228, 218, 0.73);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.game-over p {
    font-size: 3rem;
    color: #776e65;
    margin-bottom: 20px;
}

#restart-btn {
    background-color: #8f7a66;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#restart-btn:hover {
    background-color: #9f8a76;
}

.hidden {
    display: none !important;
}

footer {
    text-align: center;
    margin-top: 20px;
    font-size: 1rem;
    color: #ff0000; 
}
