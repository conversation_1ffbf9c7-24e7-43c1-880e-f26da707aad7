<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2048 Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <main class="game-container">
        <header class="header" role="banner">
            <h1>2048</h1>
            <div class="score-contain" role="region" aria-label="Score and Timer">
                <div class="score">Score: <span id="score" aria-live="polite">0</span></div>
                <div class="best-score">Best: <span id="best-score" aria-live="polite">0</span></div>
                <div class="timer">Timer: <span id="timer" aria-live="polite">00:00</span></div>
            </div>
        </header>
        <section class="game-contain">
            <div class="grid" role="grid" aria-label="2048 Game Grid"></div>
            <div class="game-over hidden" role="alert" aria-live="assertive">
                <h2>Game Over!</h2>
                <p id="final-score" aria-label="Final Score"></p>
                <p id="final-time" aria-label="Final Time"></p>
                <button id="restart-btn" class="btn">New Game</button>
            </div>
            <button id="restart-btn" 
                    class="btn btn--primary"
                    aria-label="开始新游戏">
                重新开始
            </button>
        </section>
    </main>
    <script src="game.js" defer></script>
    <footer role="contentinfo">
        <p>&copy; 2025 2048 Game. All rights reserved.</p>
    </footer>
</body>
</html>
