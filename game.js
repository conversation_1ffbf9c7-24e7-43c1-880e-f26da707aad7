class Game2048 {
    constructor() {
        this.grid = Array(4).fill().map(() => Array(4).fill(0));
        this.score = 0;
        this.bestScore = localStorage.getItem('bestScore') || 0;
        this.timeElapsed = 0;
        this.timerInterval = null;
        this.isGameWon = false;
        this.initializeGrid();
        this.setupEventListeners();
        this.updateScoreDisplay();
        this.startTimer();
    }

    initializeGrid() {
        const gridElement = document.querySelector('.grid');
        gridElement.innerHTML = '';
        
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const cell = document.createElement('div');
                cell.classList.add('grid-cell');
                cell.dataset.row = i;
                cell.dataset.col = j;
                gridElement.appendChild(cell);
            }
        }
        
        this.addRandomTile();
        this.addRandomTile();
        this.renderGrid();
    }

    addRandomTile() {
        const emptyCells = [];
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 0) {
                    emptyCells.push({row: i, col: j});
                }
            }
        }

        if (emptyCells.length > 0) {
            const {row, col} = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            this.grid[row][col] = Math.random() < 0.9 ? 2 : 4;
        }
    }

    renderGrid() {
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const cell = document.querySelector(`.grid-cell[data-row="${i}"][data-col="${j}"]`);
                cell.textContent = this.grid[i][j] || '';
                cell.className = 'grid-cell';
                if (this.grid[i][j] !== 0) {
                    cell.classList.add(`tile-${this.grid[i][j]}`);
                }
            }
        }
    }

    move(direction) {
        let moved = false;
        const newGrid = this.rotateGrid(this.grid, direction);
        
        for (let i = 0; i < 4; i++) {
            const row = newGrid[i];
            const mergedRow = this.mergeTiles(row);
            const paddedRow = [...mergedRow, ...Array(4 - mergedRow.length).fill(0)];
            
            if (JSON.stringify(row) !== JSON.stringify(paddedRow)) {
                moved = true;
            }
            newGrid[i] = paddedRow;
        }
        
        this.grid = this.rotateGrid(newGrid, (4 - direction) % 4);
        
        if (moved) {
            this.addRandomTile();
            this.renderGrid();
            this.updateScore();
            
            if (this.checkWin() && !this.isGameWon) {
                this.showWin();
            } else if (this.isGameOver()) {
                this.showGameOver();
            }
        }
    }

    mergeTiles(row) {
        const filteredRow = row.filter(tile => tile !== 0);
        const mergedRow = [];
        
        for (let i = 0; i < filteredRow.length; i++) {
            if (i < filteredRow.length - 1 && filteredRow[i] === filteredRow[i + 1]) {
                mergedRow.push(filteredRow[i] * 2);
                this.score += filteredRow[i] * 2;
                i++; // 跳过下一个相同的元素
            } else {
                mergedRow.push(filteredRow[i]);
            }
        }
        
        return mergedRow;
    }

    rotateGrid(grid, times) {
        let rotatedGrid = grid.map(row => [...row]);
        
        for (let i = 0; i < times; i++) {
            rotatedGrid = rotatedGrid[0].map((_, index) => 
                rotatedGrid.map(row => row[row.length - 1 - index])
            );
        }
        
        return rotatedGrid;
    }

    updateScore() {
        this.bestScore = Math.max(this.score, this.bestScore);
        localStorage.setItem('bestScore', this.bestScore);
        this.updateScoreDisplay();
    }

    updateScoreDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('best-score').textContent = this.bestScore;
    }

    checkWin() {
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 2048) {
                    return true;
                }
            }
        }
        return false;
    }

    isGameOver() {
        // 检查是否还有空格
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (this.grid[i][j] === 0) return false;
            }
        }
        
        // 检查是否还能合并
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (j < 3 && this.grid[i][j] === this.grid[i][j + 1]) return false;
                if (i < 3 && this.grid[i][j] === this.grid[i + 1][j]) return false;
            }
        }

        return true;
    }

    showWin() {
        this.isGameWon = true;
        setTimeout(() => {
            alert('恭喜！您达到了2048！您可以继续游戏或重新开始。');
        }, 300);
    }

    showGameOver() {
        document.querySelector('.game-over').classList.remove('hidden');
        this.stopTimer();
    }

    restart() {
        this.grid = Array(4).fill().map(() => Array(4).fill(0));
        this.score = 0;
        this.timeElapsed = 0;
        this.isGameWon = false;
        document.querySelector('.game-over').classList.add('hidden');
        this.resetTimer();
        this.initializeGrid();
    }

    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            e.preventDefault();
            switch(e.key) {
                case 'ArrowUp': this.move(0); break;
                case 'ArrowRight': this.move(1); break;
                case 'ArrowDown': this.move(2); break;
                case 'ArrowLeft': this.move(3); break;
            }
        });

        document.getElementById('restart-btn').addEventListener('click', () => this.restart());
        document.getElementById('restart-btn-game-over').addEventListener('click', () => this.restart());
        
        // 触摸支持
        this.setupTouchControls();
    }

    setupTouchControls() {
        const gameContainer = document.querySelector('.game-container');
        let startX, startY;
        
        gameContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            e.preventDefault();
        });
        
        gameContainer.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            const minSwipeDistance = 50;
            
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                if (Math.abs(deltaX) > minSwipeDistance) {
                    if (deltaX > 0) {
                        this.move(1); // 右
                    } else {
                        this.move(3); // 左
                    }
                }
            } else {
                if (Math.abs(deltaY) > minSwipeDistance) {
                    if (deltaY > 0) {
                        this.move(2); // 下
                    } else {
                        this.move(0); // 上
                    }
                }
            }
            
            startX = null;
            startY = null;
            e.preventDefault();
        });
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            this.timeElapsed++;
            const minutes = String(Math.floor(this.timeElapsed / 60)).padStart(2, '0');
            const seconds = String(this.timeElapsed % 60).padStart(2, '0');
            document.getElementById('timer').textContent = `${minutes}:${seconds}`;
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }

    resetTimer() {
        this.stopTimer();
        this.timeElapsed = 0;
        document.getElementById('timer').textContent = '00:00';
        this.startTimer();
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new Game2048();
});
